"""
    加载并验证Hartley实验数据
    dataExportFolders: 数据导出文件夹路径数组
    coneTypes: 锥细胞类型标识符
    """
    dataExportFolders = dataExportFolder
    coneTypes=["L", "M", "S", "grey"]
    dataFiles = []
    loadedData = []

    println("正在加载Hartley数据文件...")

    for (i, folder) in enumerate(dataExportFolders)
        try
            # 根据您的修复方案，现在应该查找JLD2文件
            datafile = matchfile(Regex("[A-Za-z0-9]+_[A-Za-z0-9]+_[A-Za-z0-9]+_[A-Za-z0-9]+_[A-Za-z0-9]+_tuning_result_[A-Za-z0-9]*.jld2)"), dir=folder, join=true)

            push!(dataFiles, datafile)

            # 加载数据并验证
            data = load(datafile)
            if !haskey(data, "result")
                error("数据文件 $datafile 缺少 'result' 字段")
            end

            push!(loadedData, data)
            println("✓ 成功加载 $(coneTypes[i]) 条件数据: $(basename(datafile))")

        catch e
            println("✗ 加载第 $i 个数据文件时出错 ($(coneTypes[i])): $e")
            rethrow(e)
        end
    end

    return dataFiles, loadedData