using NeuroAnalysis,Statistics,StatsBase,FileIO,Images,Plots,LsqFit,FFTW,CSVFiles,DataFrames
# Expt info
disk = "/media/vcl/vcl003/"
subject = "AF3"  # Animal

recordSession = "002"
testId = ["003","004","005","006"]

recordPlane = "001"
delays = collect(-0.066:0.033:0.4)
print(collect(delays))

lbTime = 0.198
ubTime = 0.330
blkTime = 0.099
respThres = 0.25

# cw = datasetFinal["coneweight"]
# achroResp = datasetFinal["achroResp"]
# CSV.write(joinpath(resultFolder,join([subject,"_",recordSession,"_",recordPlane,"_thres",respThres,"_sta_dataset.csv"])), cw)
# CSV.write(joinpath(resultFolder,join([subject,"_",recordSession,"_",recordPlane,"_thres",respThres,"_achrosta_dataset.csv"])), achroResp)
## Prepare data & result path
siteId=[]
for i =1:size(testId,1)
    siteid = join(["$(recordSession)_", testId[i], "_$(recordPlane)"])
    push!(siteId,siteid)
end

dataFolder = joinpath.(disk,subject, "2P_analysis", join(["U",recordSession]))
dataExportFolder = joinpath.(disk,subject, "2P_analysis", join(["U",recordSession]), siteId, "Plots")
resultFolder = joinpath.(disk,subject, "2P_analysis", join(["U",recordSession]), "_Summary", "DataExport")
resultFolderPlot = joinpath.(disk,subject, "2P_analysis", join(["U",recordSession]), "_Summary", join(["plane_",recordPlane]),"0. Original maps","Fourier")
isdir(resultFolder) || mkpath(resultFolder)
isdir(resultFolderPlot) || mkpath(resultFolderPlot)

## Load and merge Hartley Fourier data
function load_hartley_data(dataExportFolders, coneTypes=["L", "M", "S", "grey"])
    """
    加载并验证Hartley实验数据
    dataExportFolders: 数据导出文件夹路径数组
    coneTypes: 锥细胞类型标识符
    """
    dataFiles = []
    loadedData = []

    println("正在加载Hartley数据文件...")

    for (i, folder) in enumerate(dataExportFolders)
        try
            # 根据您的修复方案，现在应该查找JLD2文件
            datafile = matchfile(Regex("[A-Za-z0-9]*[A-Za-z0-9]*_[A-Za-z0-9]*_[A-Za-z0-9]*_tuning_result.jld2"), dir=folder, join=true)

            if isempty(datafile)
                # 如果没找到JLD2文件，尝试CSV文件作为备选
                datafile = matchfile(Regex("[A-Za-z0-9]*[A-Za-z0-9]*_[A-Za-z0-9]*_[A-Za-z0-9]*_tuning_result.csv"), dir=folder, join=true)
                if isempty(datafile)
                    error("在文件夹 $folder 中未找到数据文件")
                end
            end

            push!(dataFiles, datafile)

            # 加载数据并验证
            data = load(datafile)
            if !haskey(data, "result")
                error("数据文件 $datafile 缺少 'result' 字段")
            end

            push!(loadedData, data)
            println("✓ 成功加载 $(coneTypes[i]) 条件数据: $(basename(datafile))")

        catch e
            println("✗ 加载第 $i 个数据文件时出错 ($(coneTypes[i])): $e")
            rethrow(e)
        end
    end

    return dataFiles, loadedData
end

function validate_data_consistency(loadedData, coneTypes=["L", "M", "S", "A"])
    """
    验证加载数据的一致性
    """
    println("验证数据一致性...")

    if length(loadedData) != length(coneTypes)
        error("数据文件数量 ($(length(loadedData))) 与锥细胞类型数量 ($(length(coneTypes))) 不匹配")
    end

    # 检查细胞数量一致性
    cellCounts = [length(data["result"].cellId) for data in loadedData]
    if !all(x -> x == cellCounts[1], cellCounts)
        println("警告: 不同条件下的细胞数量不一致: $cellCounts")
    end

    # 检查kernel尺寸一致性
    kernelSizes = [size(data["result"].kernnor[1]) for data in loadedData]
    if !all(x -> x == kernelSizes[1], kernelSizes)
        error("不同条件下的kernel尺寸不一致: $kernelSizes")
    end

    println("✓ 数据一致性验证通过")
    println("  - 细胞数量: $(cellCounts[1])")
    println("  - Kernel尺寸: $(kernelSizes[1])")

    return true
end

# 加载数据文件
dataFiles, loadedData = load_hartley_data(dataExportFolder, ["L", "M", "S", "A"])

# 验证数据一致性
validate_data_consistency(loadedData, ["L", "M", "S", "A"])

# 合并数据
println("正在合并Hartley Fourier数据...")
dataset = sbxjoinhartleyFourier(loadedData)
println("✓ 数据合并完成")

# 保存合并后的数据集
outputFile = joinpath(resultFolder,join([subject,"_",recordSession,"_",recordPlane,"_thres",respThres,"_fourier_dataset.jld2"]))
try
    save(outputFile, "dataset", dataset)
    println("✓ 合并数据集已保存到: $(basename(outputFile))")
catch e
    println("✗ 保存合并数据集时出错: $e")
    # 尝试分别保存关键组件
    println("尝试分别保存数据组件...")
    try
        save(joinpath(resultFolder, "kern_data.jld2"), "kern", dataset["kern"])
        save(joinpath(resultFolder, "metadata.jld2"), "signif", dataset["signif"], "taumax", dataset["taumax"])
        println("✓ 数据组件已分别保存")
    catch e2
        println("✗ 分别保存也失败: $e2")
    end
end
## 加载已保存的数据集（如果存在）
function load_existing_dataset(resultFolder, subject, recordSession, recordPlane, respThres)
    """
    尝试加载已存在的合并数据集
    """
    datasetFile = joinpath(resultFolder, join([subject,"_",recordSession,"_",recordPlane,"_thres",respThres,"_fourier_dataset.jld2"]))

    if isfile(datasetFile)
        try
            dataset = load(datasetFile, "dataset")
            println("✓ 成功加载已存在的数据集: $(basename(datasetFile))")
            return dataset, true
        catch e
            println("✗ 加载已存在数据集失败: $e")
            println("将重新生成数据集...")
            return nothing, false
        end
    else
        println("未找到已存在的数据集，将生成新的数据集...")
        return nothing, false
    end
end

# 尝试加载已存在的数据集
dataset, loaded_existing = load_existing_dataset(resultFolder, subject, recordSession, recordPlane, respThres)

# 如果没有加载到已存在的数据集，则重新生成
if !loaded_existing
    # 这里会执行上面的数据加载和合并代码
    println("重新执行数据加载和合并流程...")
end

## 数据质量检查
function check_dataset_quality(dataset)
    """
    检查合并后数据集的质量
    """
    println("检查数据集质量...")

    required_fields = ["kern", "signif", "taumax", "kstdmax", "kdelta", "oriraw", "sfraw"]
    missing_fields = []

    for field in required_fields
        if !haskey(dataset, field)
            push!(missing_fields, field)
        end
    end

    if !isempty(missing_fields)
        println("✗ 缺少必要字段: $missing_fields")
        return false
    end

    # 检查数据完整性
    cell_ids = collect(keys(dataset["kern"]))
    n_cells = length(cell_ids)

    println("数据集统计:")
    println("  - 细胞数量: $n_cells")
    println("  - 细胞ID范围: $(minimum(cell_ids)) - $(maximum(cell_ids))")

    # 检查每个细胞的数据完整性
    incomplete_cells = []
    for cell_id in cell_ids
        if !haskey(dataset["signif"], cell_id) ||
           !haskey(dataset["kern"], cell_id) ||
           !haskey(dataset["oriraw"], cell_id) ||
           !haskey(dataset["sfraw"], cell_id)
            push!(incomplete_cells, cell_id)
        end
    end

    if !isempty(incomplete_cells)
        println("✗ 数据不完整的细胞: $incomplete_cells")
        return false
    end

    # 检查kernel数据
    kern_sizes = [size(dataset["kern"][id]) for id in cell_ids]
    if !all(s -> s == kern_sizes[1], kern_sizes)
        println("✗ Kernel尺寸不一致")
        return false
    end

    println("✓ 数据集质量检查通过")
    println("  - Kernel尺寸: $(kern_sizes[1])")

    return true
end

# 检查数据集质量
if dataset !== nothing
    quality_ok = check_dataset_quality(dataset)
    if !quality_ok
        error("数据集质量检查失败，请检查数据")
    end
else
    error("数据集为空，无法继续处理")
end

## 绘图数据准备
function prepare_plot_data(dataset, cell_id)
    """
    为指定细胞准备绘图数据
    """
    try
        kern = copy(dataset["kern"][cell_id])

        # 处理无效值
        replace!(kern, -Inf=>0, Inf=>0, NaN=>0)

        # 归一化
        maxmag = maximum(abs.(kern))
        if maxmag > 0
            kern = kern ./ maxmag
        else
            println("警告: 细胞 $cell_id 的kernel最大值为0")
        end

        return kern, maxmag
    catch e
        println("✗ 准备细胞 $cell_id 的绘图数据时出错: $e")
        return nothing, nothing
    end
end

## Plot Hartley Fourier image
println("开始生成Hartley Fourier图像...")

# 获取所有细胞ID并排序
cell_ids = sort(collect(keys(dataset["kern"])))
colors = ["L", "M", "S", "A"]
truestimsz = 12   # 刺激尺寸 (度)

# 创建输出目录
isdir(resultFolderPlot) || mkpath(resultFolderPlot)

successful_plots = 0
failed_plots = 0

for u in cell_ids
    try
        # 准备绘图数据
        kern, maxmag = prepare_plot_data(dataset, u)

        if kern === nothing
            failed_plots += 1
            continue
        end

        # 设置绘图参数
        imagesize = size(kern)[1]
        colorbar_hack = zeros(size(kern))
        xylim = [0, round(truestimsz, digits=1)]
        xy = range(xylim..., length=imagesize)

        # 创建绘图
        p = Plots.plot(layout=(1,5), legend=false, size=(1650,600))

        # 绘制四个锥细胞类型的heatmap
        foreach(c->Plots.heatmap!(p, subplot=c, xy, xy, kern[:,:,c],
                                 aspect_ratio=:equal, frame=:grid,
                                 color=:bwr, clims=(-1,1),
                                 xlims=xylim, ylims=xylim,
                                 xticks=[], yticks=[], yflip=true,
                                 xlabel=string(colors[c]),
                                 title="Cell_$(u)_Fourier"), 1:4)

        # 添加颜色条
        heatmap!(p, subplot=5, xy, xy, colorbar_hack[:,:,1],
                aspect_ratio=:equal, frame=:grid,
                xticks=[], yticks=[],
                color=:bwr, clims=(-maxmag, maxmag),
                colorbar=:left)

        # 添加参考线
        foreach(c->Plots.plot!(p, subplot=c, [6], seriestype="vline",
                              linecolor=:gray, linestyle=:dot,
                              linewidth=3, linealpha=1,
                              xticks=([6],["0"]), label=""), 1:4)
        foreach(c->Plots.plot!(p, subplot=c, [6], seriestype="hline",
                              linecolor=:gray, linestyle=:dot,
                              linewidth=3, linealpha=1, label=""), 1:4)
        foreach(c->Plots.plot!(p, subplot=c, yticks=([6],["0"])), 1)

        # 保存图像
        output_filename = joinpath(resultFolderPlot, join([subject,"_U",recordSession,"_Plane",recordPlane, "_Cell",u,".png"]))
        savefig(output_filename)

        successful_plots += 1

    catch e
        println("✗ 绘制细胞 $u 的图像时出错: $e")
        failed_plots += 1
    end
end

# 输出绘图统计
println("绘图完成:")
println("  - 成功: $successful_plots")
println("  - 失败: $failed_plots")
println("  - 图像保存路径: $resultFolderPlot")

## 数据导出功能（可选）
function export_summary_data(dataset, resultFolder, subject, recordSession, recordPlane, respThres)
    """
    导出汇总数据到CSV文件
    """
    try
        println("导出汇总数据...")

        # 准备汇总数据
        cell_ids = sort(collect(keys(dataset["kern"])))
        summary_data = []

        for cell_id in cell_ids
            # 提取每个细胞的关键指标
            row = Dict(
                "CellID" => cell_id,
                "Subject" => subject,
                "RecordSession" => recordSession,
                "RecordPlane" => recordPlane,
                "RespThreshold" => respThres
            )

            # 添加各锥细胞类型的数据
            cone_types = ["L", "M", "S", "A"]
            for (i, cone) in enumerate(cone_types)
                if haskey(dataset["signif"], cell_id) && size(dataset["signif"][cell_id], 2) >= i
                    row["$(cone)_Signif"] = dataset["signif"][cell_id][1, i]
                end
                if haskey(dataset["taumax"], cell_id) && size(dataset["taumax"][cell_id], 2) >= i
                    row["$(cone)_TauMax"] = dataset["taumax"][cell_id][1, i]
                end
                if haskey(dataset["kstdmax"], cell_id) && size(dataset["kstdmax"][cell_id], 2) >= i
                    row["$(cone)_KstdMax"] = dataset["kstdmax"][cell_id][1, i]
                end
                if haskey(dataset["orimax"], cell_id) && size(dataset["orimax"][cell_id], 2) >= i
                    row["$(cone)_OriMax"] = dataset["orimax"][cell_id][1, i]
                end
                if haskey(dataset["sfmax"], cell_id) && size(dataset["sfmax"][cell_id], 2) >= i
                    row["$(cone)_SfMax"] = dataset["sfmax"][cell_id][1, i]
                end
            end

            push!(summary_data, row)
        end

        # 转换为DataFrame并保存
        using DataFrames
        df = DataFrame(summary_data)

        csv_filename = joinpath(resultFolder, join([subject,"_",recordSession,"_",recordPlane,"_thres",respThres,"_summary.csv"]))
        CSV.write(csv_filename, df)

        println("✓ 汇总数据已导出到: $(basename(csv_filename))")
        return true

    catch e
        println("✗ 导出汇总数据时出错: $e")
        return false
    end
end

# 导出汇总数据（可选，取消注释以启用）
# export_summary_data(dataset, resultFolder, subject, recordSession, recordPlane, respThres)

## 主执行函数
function run_hartley_analysis(;
    disk = "/media/vcl/vcl003/",
    subject = "AF3",
    recordSession = "002",
    testId = ["003","004","005","006"],
    recordPlane = "001",
    respThres = 0.25,
    export_csv = false,
    force_reload = false)
    """
    运行完整的Hartley分析流程

    参数:
    - disk: 数据磁盘路径
    - subject: 实验动物ID
    - recordSession: 记录会话ID
    - testId: 测试ID数组 (L, M, S, A的顺序)
    - recordPlane: 记录平面ID
    - respThres: 响应阈值
    - export_csv: 是否导出CSV汇总数据
    - force_reload: 是否强制重新加载数据
    """

    println("="^60)
    println("开始Hartley Fourier分析")
    println("实验参数:")
    println("  - 动物: $subject")
    println("  - 会话: $recordSession")
    println("  - 平面: $recordPlane")
    println("  - 测试ID: $testId")
    println("  - 响应阈值: $respThres")
    println("="^60)

    try
        # 准备路径
        siteId = [join(["$(recordSession)_", tid, "_$(recordPlane)"]) for tid in testId]
        dataExportFolder = [joinpath(disk, subject, "2P_analysis", join(["U",recordSession]), sid, "Plots") for sid in siteId]
        resultFolder = joinpath(disk, subject, "2P_analysis", join(["U",recordSession]), "_Summary", "DataExport")
        resultFolderPlot = joinpath(disk, subject, "2P_analysis", join(["U",recordSession]), "_Summary", join(["plane_",recordPlane]), "0. Original maps", "Fourier")

        # 创建输出目录
        isdir(resultFolder) || mkpath(resultFolder)
        isdir(resultFolderPlot) || mkpath(resultFolderPlot)

        # 检查是否需要重新加载数据
        dataset = nothing
        if !force_reload
            dataset, loaded_existing = load_existing_dataset(resultFolder, subject, recordSession, recordPlane, respThres)
        end

        # 如果没有加载到现有数据集，则重新生成
        if dataset === nothing
            # 加载和合并数据
            dataFiles, loadedData = load_hartley_data(dataExportFolder, ["L", "M", "S", "A"])
            validate_data_consistency(loadedData, ["L", "M", "S", "A"])
            dataset = sbxjoinhartleyFourier(loadedData)

            # 保存合并后的数据集
            outputFile = joinpath(resultFolder, join([subject,"_",recordSession,"_",recordPlane,"_thres",respThres,"_fourier_dataset.jld2"]))
            try
                save(outputFile, "dataset", dataset)
                println("✓ 合并数据集已保存")
            catch e
                println("✗ 保存合并数据集失败: $e")
            end
        end

        # 数据质量检查
        if !check_dataset_quality(dataset)
            error("数据集质量检查失败")
        end

        # 生成图像
        println("开始生成Hartley Fourier图像...")
        cell_ids = sort(collect(keys(dataset["kern"])))
        colors = ["L", "M", "S", "A"]
        truestimsz = 12

        successful_plots = 0
        failed_plots = 0

        for u in cell_ids
            try
                kern, maxmag = prepare_plot_data(dataset, u)
                if kern === nothing
                    failed_plots += 1
                    continue
                end

                imagesize = size(kern)[1]
                colorbar_hack = zeros(size(kern))
                xylim = [0, round(truestimsz, digits=1)]
                xy = range(xylim..., length=imagesize)

                p = Plots.plot(layout=(1,5), legend=false, size=(1650,600))

                foreach(c->Plots.heatmap!(p, subplot=c, xy, xy, kern[:,:,c],
                                         aspect_ratio=:equal, frame=:grid,
                                         color=:bwr, clims=(-1,1),
                                         xlims=xylim, ylims=xylim,
                                         xticks=[], yticks=[], yflip=true,
                                         xlabel=string(colors[c]),
                                         title="Cell_$(u)_Fourier"), 1:4)

                heatmap!(p, subplot=5, xy, xy, colorbar_hack[:,:,1],
                        aspect_ratio=:equal, frame=:grid,
                        xticks=[], yticks=[],
                        color=:bwr, clims=(-maxmag, maxmag),
                        colorbar=:left)

                foreach(c->Plots.plot!(p, subplot=c, [6], seriestype="vline",
                                      linecolor=:gray, linestyle=:dot,
                                      linewidth=3, linealpha=1,
                                      xticks=([6],["0"]), label=""), 1:4)
                foreach(c->Plots.plot!(p, subplot=c, [6], seriestype="hline",
                                      linecolor=:gray, linestyle=:dot,
                                      linewidth=3, linealpha=1, label=""), 1:4)
                foreach(c->Plots.plot!(p, subplot=c, yticks=([6],["0"])), 1)

                output_filename = joinpath(resultFolderPlot, join([subject,"_U",recordSession,"_Plane",recordPlane, "_Cell",u,".png"]))
                savefig(output_filename)
                successful_plots += 1

            catch e
                println("✗ 绘制细胞 $u 失败: $e")
                failed_plots += 1
            end
        end

        println("绘图完成: 成功 $successful_plots, 失败 $failed_plots")

        # 导出CSV数据（如果需要）
        if export_csv
            export_summary_data(dataset, resultFolder, subject, recordSession, recordPlane, respThres)
        end

        println("="^60)
        println("Hartley Fourier分析完成!")
        println("结果保存在: $resultFolder")
        println("图像保存在: $resultFolderPlot")
        println("="^60)

        return dataset

    catch e
        println("✗ 分析过程中出错: $e")
        rethrow(e)
    end
end

# 使用当前参数运行分析
dataset = run_hartley_analysis(
    disk = disk,
    subject = subject,
    recordSession = recordSession,
    testId = testId,
    recordPlane = recordPlane,
    respThres = respThres,
    export_csv = false,  # 设置为true以导出CSV
    force_reload = false  # 设置为true以强制重新加载数据
)

## Plot example cell

##
